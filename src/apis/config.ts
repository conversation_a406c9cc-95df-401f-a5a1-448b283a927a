import http from '@/lib/http';

// 用户头像配置保存请求接口
export interface ISaveUserAvatarRequest {
  user_id: string;
  avatar_url: string;
}

// 用户头像配置保存响应接口
export interface ISaveUserAvatarResponse {
  result: string;
  message: string;
  config: {
    user_id: string;
    avatar_url: string;
  };
}

// 用户头像配置获取响应接口
export interface IGetUserAvatarResponse {
  result: string;
  config: {
    user_id: string;
    avatar_url: string;
    last_updated: string;
  } | null;
  message?: string;
}

// 老董头像URL映射
export const LAODONG_AVATAR_URLS = {
  laodong1:
    'https://msstest.sankuai.com/aigc-public-resources/avatar/32c198a3-3f65-457b-b61d-dd1506f32634_1755502522714.jpg',
  laodong2:
    'https://msstest.sankuai.com/aigc-public-resources/avatar/9489ef65-f518-47f9-a1a3-9f82dc431bb6_1755502582220.jpg',
  laodong3:
    'https://msstest.sankuai.com/aigc-public-resources/avatar/7baae8ab-412a-4520-97bc-d82527577274_1755502612994.png',
  laodong4:
    'https://msstest.sankuai.com/aigc-public-resources/avatar/ee74a519-bceb-44fe-a0a3-b3c879b7a97f_1755502637835.png',
  laodong5:
    'https://msstest.sankuai.com/aigc-public-resources/avatar/e0043b48-a896-4747-bdb4-21db83ea597f_1755502667098.png',
  laodong6:
    'https://msstest.sankuai.com/aigc-public-resources/avatar/f5c6c04a-247f-4178-a586-f0a88087ad9f_1755502696207.jpg',
};

// 保存用户选择的老董头像
export function saveUserAvatar(params: ISaveUserAvatarRequest): Promise<ISaveUserAvatarResponse> {
  return http({
    url: '/humanrelation/user_config/avatar/save',
    method: 'post',
    data: params,
  });
}

// 获取用户选择的老董头像
export function getUserAvatar(userId: string): Promise<IGetUserAvatarResponse> {
  return http({
    url: `/humanrelation/user_config/avatar/${userId}`,
    method: 'get',
  });
}
